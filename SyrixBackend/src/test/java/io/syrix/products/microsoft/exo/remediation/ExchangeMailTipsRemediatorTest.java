package io.syrix.products.microsoft.exo.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.datamodel.task.remediation.exchange.ExchangeRemediationConfig;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.exo.ExoConstants;
import io.syrix.products.microsoft.exo.remediation.context.ExchangeRemediationContext;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Unit tests for ExchangeMailTipsRemediator (MS.EXO.7.2v1)
 * 
 * Tests verify the MailTips enablement functionality including:
 * - Configuration loading and validation
 * - Remediation logic for enabling MailTips
 * - Rollback functionality
 * - Error handling scenarios
 */
@ExtendWith(MockitoExtension.class)
class ExchangeMailTipsRemediatorTest {

    @Mock
    private MicrosoftGraphClient mockGraphClient;

    @Mock
    private PowerShellClient mockPowerShellClient;

    @Mock
    private ExchangeRemediationContext mockContext;

    @Mock
    private ExchangeRemediationConfig mockRemediationConfig;

    private ObjectMapper objectMapper;
    private ExchangeMailTipsRemediator remediator;
    private ObjectNode mockConfigNode;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        mockConfigNode = objectMapper.createObjectNode();
        
        remediator = new ExchangeMailTipsRemediator(
            mockGraphClient, 
            mockPowerShellClient, 
            mockConfigNode, 
            mockContext, 
            mockRemediationConfig
        );
    }

    @Test
    void testRemediateWithMailTipsAlreadyEnabled() throws Exception {
        // Arrange - MailTips already enabled in organization config
        ObjectNode orgConfig = objectMapper.createObjectNode();
        orgConfig.put(ExoConstants.MAILTIPS_ALL_TIPS_ENABLED, true);
        orgConfig.put(ExoConstants.MAILTIPS_MAILBOX_SOURCED_TIPS_ENABLED, true);
        orgConfig.put(ExoConstants.MAILTIPS_EXTERNAL_RECIPIENTS_ENABLED, true);
        orgConfig.put(ExoConstants.MAILTIPS_GROUP_METRICS_ENABLED, true);
        mockConfigNode.set(ExoConstants.CONFIG_KEY_ORGANIZATION, objectMapper.createArrayNode().add(orgConfig));

        // Act
        CompletableFuture<PolicyChangeResult> result = remediator.updateMailTipsProperties();
        PolicyChangeResult changeResult = result.get();

        // Assert
        assertEquals("MS.EXO.7.2v1", changeResult.getPolicyId());
        assertEquals("REQUIREMENT_MET", changeResult.getStatus());
        assertEquals(ExoConstants.SUCCESS_MAILTIPS_ALREADY_ENABLED, changeResult.getMessage());
        
        // Verify no PowerShell commands were executed since MailTips already enabled
        verify(mockPowerShellClient, never()).executeCmdletCommand(any());
    }

    @Test
    void testRemediateEnablesMailTipsSuccessfully() throws Exception {
        // Arrange - MailTips not enabled in organization config
        ObjectNode orgConfig = objectMapper.createObjectNode();
        orgConfig.put(ExoConstants.MAILTIPS_ALL_TIPS_ENABLED, false);
        orgConfig.put(ExoConstants.MAILTIPS_MAILBOX_SOURCED_TIPS_ENABLED, false);
        mockConfigNode.set(ExoConstants.CONFIG_KEY_ORGANIZATION, objectMapper.createArrayNode().add(orgConfig));

        // Mock successful PowerShell execution for Set-OrganizationConfig
        JsonNode setConfigResult = objectMapper.createArrayNode();
        when(mockPowerShellClient.executeCmdletCommand(any()))
            .thenReturn(CompletableFuture.completedFuture(setConfigResult));

        // Mock verification response showing MailTips enabled
        ObjectNode verificationResponse = objectMapper.createObjectNode();
        verificationResponse.put(ExoConstants.MAILTIPS_ALL_TIPS_ENABLED, true);
        verificationResponse.put(ExoConstants.MAILTIPS_MAILBOX_SOURCED_TIPS_ENABLED, true);
        JsonNode verificationArray = objectMapper.createArrayNode().add(verificationResponse);
        
        when(mockPowerShellClient.executeCmdletCommand(argThat(request -> 
            ExoConstants.GET_ORGANIZATION_CONFIG.equals(request.getCmdletName()))))
            .thenReturn(CompletableFuture.completedFuture(verificationArray));

        // Act
        CompletableFuture<PolicyChangeResult> result = remediator.updateMailTipsProperties();
        PolicyChangeResult changeResult = result.get();

        // Assert
        assertEquals("MS.EXO.7.2v1", changeResult.getPolicyId());
        assertEquals("SUCCESS", changeResult.getStatus());
        assertEquals(ExoConstants.SUCCESS_MAILTIPS_ENABLED, changeResult.getMessage());

        // Verify Set-OrganizationConfig was called with correct parameters
        ArgumentCaptor<PowerShellClient.CommandRequest> captor = 
            ArgumentCaptor.forClass(PowerShellClient.CommandRequest.class);
        verify(mockPowerShellClient, times(2)).executeCmdletCommand(captor.capture());

        PowerShellClient.CommandRequest setConfigRequest = captor.getAllValues().get(0);
        assertEquals(ExoConstants.SET_ORGANIZATION_CONFIG, setConfigRequest.getCmdletName());
        
        Map<String, Object> params = setConfigRequest.getParameters();
        assertEquals(true, params.get(ExoConstants.MAILTIPS_ALL_TIPS_ENABLED));
        assertEquals(true, params.get(ExoConstants.MAILTIPS_MAILBOX_SOURCED_TIPS_ENABLED));
        assertEquals(ExoConstants.ERROR_ACTION_STOP, params.get(ExoConstants.ERROR_ACTION));
    }

    @Test
    void testRemediateFailsWhenConfigurationNotFound() throws Exception {
        // Arrange - No organization config available
        mockConfigNode = null;
        remediator = new ExchangeMailTipsRemediator(
            mockGraphClient, 
            mockPowerShellClient, 
            mockConfigNode, 
            mockContext, 
            mockRemediationConfig
        );

        // Act
        CompletableFuture<PolicyChangeResult> result = remediator.updateMailTipsProperties();
        PolicyChangeResult changeResult = result.get();

        // Assert
        assertEquals("MS.EXO.7.2v1", changeResult.getPolicyId());
        assertEquals("FAILED", changeResult.getStatus());
        assertEquals(ExoConstants.ERROR_NO_MAILTIPS_CONFIG, changeResult.getMessage());
        
        verify(mockPowerShellClient, never()).executeCmdletCommand(any());
    }

    @Test
    void testRemediateFailsWhenVerificationFails() throws Exception {
        // Arrange - MailTips not enabled in organization config
        ObjectNode orgConfig = objectMapper.createObjectNode();
        orgConfig.put(ExoConstants.MAILTIPS_ALL_TIPS_ENABLED, false);
        orgConfig.put(ExoConstants.MAILTIPS_MAILBOX_SOURCED_TIPS_ENABLED, false);
        mockConfigNode.set(ExoConstants.CONFIG_KEY_ORGANIZATION, objectMapper.createArrayNode().add(orgConfig));

        // Mock successful Set-OrganizationConfig execution
        when(mockPowerShellClient.executeCmdletCommand(argThat(request -> 
            ExoConstants.SET_ORGANIZATION_CONFIG.equals(request.getCmdletName()))))
            .thenReturn(CompletableFuture.completedFuture(objectMapper.createArrayNode()));

        // Mock verification response showing MailTips still not enabled
        ObjectNode verificationResponse = objectMapper.createObjectNode();
        verificationResponse.put(ExoConstants.MAILTIPS_ALL_TIPS_ENABLED, false);
        verificationResponse.put(ExoConstants.MAILTIPS_MAILBOX_SOURCED_TIPS_ENABLED, false);
        JsonNode verificationArray = objectMapper.createArrayNode().add(verificationResponse);
        
        when(mockPowerShellClient.executeCmdletCommand(argThat(request -> 
            ExoConstants.GET_ORGANIZATION_CONFIG.equals(request.getCmdletName()))))
            .thenReturn(CompletableFuture.completedFuture(verificationArray));

        // Act
        CompletableFuture<PolicyChangeResult> result = remediator.updateMailTipsProperties();
        PolicyChangeResult changeResult = result.get();

        // Assert
        assertEquals("MS.EXO.7.2v1", changeResult.getPolicyId());
        assertEquals("FAILED", changeResult.getStatus());
        assertTrue(changeResult.getMessage().contains("MailTips verification failed"));
    }

    @Test
    void testRemediateHandlesPowerShellException() throws Exception {
        // Arrange - MailTips not enabled in organization config
        ObjectNode orgConfig = objectMapper.createObjectNode();
        orgConfig.put(ExoConstants.MAILTIPS_ALL_TIPS_ENABLED, false);
        orgConfig.put(ExoConstants.MAILTIPS_MAILBOX_SOURCED_TIPS_ENABLED, false);
        mockConfigNode.set(ExoConstants.CONFIG_KEY_ORGANIZATION, objectMapper.createArrayNode().add(orgConfig));

        // Mock PowerShell exception
        when(mockPowerShellClient.executeCmdletCommand(any()))
            .thenReturn(CompletableFuture.failedFuture(new RuntimeException("PowerShell execution failed")));

        // Act
        CompletableFuture<PolicyChangeResult> result = remediator.updateMailTipsProperties();
        PolicyChangeResult changeResult = result.get();

        // Assert
        assertEquals("MS.EXO.7.2v1", changeResult.getPolicyId());
        assertEquals("FAILED", changeResult.getStatus());
        assertTrue(changeResult.getMessage().contains("Error enabling MailTips"));
        assertTrue(changeResult.getMessage().contains("PowerShell execution failed"));
    }

    @Test
    void testRollbackDisablesMailTipsSuccessfully() throws Exception {
        // Arrange
        when(mockPowerShellClient.executeCmdletCommand(any()))
            .thenReturn(CompletableFuture.completedFuture(objectMapper.createArrayNode()));
        
        PolicyChangeResult mockFixResult = IPolicyRemediator.success_("MS.EXO.7.2v1", "Test fix");

        // Act
        CompletableFuture<PolicyChangeResult> result = remediator.rollback(mockFixResult);
        PolicyChangeResult rollbackResult = result.get();

        // Assert
        assertEquals("SUCCESS", rollbackResult.getStatus());
        assertEquals("MS.EXO.7.2v1", rollbackResult.getPolicyId());
        assertEquals("MailTips disabled successfully (rollback)", rollbackResult.getMessage());

        // Verify rollback parameters
        ArgumentCaptor<PowerShellClient.CommandRequest> captor = 
            ArgumentCaptor.forClass(PowerShellClient.CommandRequest.class);
        verify(mockPowerShellClient).executeCmdletCommand(captor.capture());

        PowerShellClient.CommandRequest rollbackRequest = captor.getValue();
        assertEquals(ExoConstants.SET_ORGANIZATION_CONFIG, rollbackRequest.getCmdletName());
        
        Map<String, Object> params = rollbackRequest.getParameters();
        assertEquals(false, params.get(ExoConstants.MAILTIPS_ALL_TIPS_ENABLED));
        assertEquals(false, params.get(ExoConstants.MAILTIPS_MAILBOX_SOURCED_TIPS_ENABLED));
        assertEquals(ExoConstants.ERROR_ACTION_STOP, params.get(ExoConstants.ERROR_ACTION));
    }

    @Test
    void testRollbackHandlesException() throws Exception {
        // Arrange
        when(mockPowerShellClient.executeCmdletCommand(any()))
            .thenReturn(CompletableFuture.failedFuture(new RuntimeException("Rollback failed")));
        
        PolicyChangeResult mockFixResult = IPolicyRemediator.success_("MS.EXO.7.2v1", "Test fix");

        // Act
        CompletableFuture<PolicyChangeResult> result = remediator.rollback(mockFixResult);
        PolicyChangeResult rollbackResult = result.get();

        // Assert
        assertEquals("FAILED", rollbackResult.getStatus());
        assertEquals("MS.EXO.7.2v1", rollbackResult.getPolicyId());
        assertTrue(rollbackResult.getMessage().contains("Error during MailTips rollback"));
        assertTrue(rollbackResult.getMessage().contains("Rollback failed"));
    }

    @Test
    void testRemediatePartiallyEnabledMailTips() throws Exception {
        // Arrange - Only MailTipsAllTipsEnabled is true, but MailTipsMailboxSourcedTipsEnabled is false
        ObjectNode orgConfig = objectMapper.createObjectNode();
        orgConfig.put(ExoConstants.MAILTIPS_ALL_TIPS_ENABLED, true);
        orgConfig.put(ExoConstants.MAILTIPS_MAILBOX_SOURCED_TIPS_ENABLED, false);
        mockConfigNode.set(ExoConstants.CONFIG_KEY_ORGANIZATION, objectMapper.createArrayNode().add(orgConfig));

        // Mock successful PowerShell execution
        when(mockPowerShellClient.executeCmdletCommand(any()))
            .thenReturn(CompletableFuture.completedFuture(objectMapper.createArrayNode()));

        // Mock verification showing full enablement
        ObjectNode verificationResponse = objectMapper.createObjectNode();
        verificationResponse.put(ExoConstants.MAILTIPS_ALL_TIPS_ENABLED, true);
        verificationResponse.put(ExoConstants.MAILTIPS_MAILBOX_SOURCED_TIPS_ENABLED, true);
        JsonNode verificationArray = objectMapper.createArrayNode().add(verificationResponse);
        
        when(mockPowerShellClient.executeCmdletCommand(argThat(request -> 
            ExoConstants.GET_ORGANIZATION_CONFIG.equals(request.getCmdletName()))))
            .thenReturn(CompletableFuture.completedFuture(verificationArray));

        // Act
        CompletableFuture<PolicyChangeResult> result = remediator.updateMailTipsProperties();
        PolicyChangeResult changeResult = result.get();

        // Assert - Should proceed with remediation since not fully enabled
        assertEquals("MS.EXO.7.2v1", changeResult.getPolicyId());
        assertEquals("SUCCESS", changeResult.getStatus());
        assertEquals(ExoConstants.SUCCESS_MAILTIPS_ENABLED, changeResult.getMessage());
        
        // Verify PowerShell was called to enable both settings
        verify(mockPowerShellClient, times(2)).executeCmdletCommand(any());
    }

    @Test  
    void testGetPolicyId() {
        // Act & Assert
        assertEquals("MS.EXO.7.2v1", remediator.getPolicyId());
    }
}