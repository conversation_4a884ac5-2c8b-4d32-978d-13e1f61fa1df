package io.syrix.products.microsoft.exo.remediation;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.type.CollectionType;
import io.syrix.datamodel.task.remediation.exchange.ExchangeRemediationConfig;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.IPolicyRemediatorRollback;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeStatus;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediationResult;
import io.syrix.products.microsoft.exo.ExoConstants;
import io.syrix.products.microsoft.exo.remediation.context.ExchangeRemediationContext;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * Remediates MS.EXO.8.5v1 policy by preventing users from using third-party storage 
 * providers in Outlook on the web. This implementation disables access to external 
 * storage services like Dropbox, OneDrive Consumer, Google Drive, and similar providers
 * to prevent data leakage through unauthorized external storage integrations as required 
 * by CIS Microsoft 365 Foundations Benchmark control 6.5.3.
 */
@PolicyRemediator("MS.EXO.8.5v1")
public class OWAThirdPartyStorageRemediator extends ExchangeBaseRemediator implements IPolicyRemediatorRollback {

    private static final String STORAGE_DISABLED_VALUE = "false";
    private static final String STORAGE_ENABLED_VALUE = "true";

    public OWAThirdPartyStorageRemediator(MicrosoftGraphClient graphClient, 
                                               PowerShellClient exchangeClient, 
                                               ObjectNode configNode, 
                                               ExchangeRemediationContext exchangeRemediationContext, 
                                               ExchangeRemediationConfig remediationConfig) {
        super(graphClient, exchangeClient, configNode, exchangeRemediationContext, remediationConfig);
    }

    @Override
    public CompletableFuture<JsonNode> remediate() {
        return remediate_().thenApply(jsonMapper::valueToTree);
    }

    @Override
    public CompletableFuture<PolicyChangeResult> remediate_() {
        logger.info("Starting remediation for {} (Third-Party Storage Providers Prevention)", getPolicyId());

        List<OwaMailboxPolicy> policies = getOwaMailboxPolicies();
        if (policies.isEmpty()) {
            return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ExoConstants.ERROR_NO_OWA_MAILBOX_POLICIES));
        }

        logger.debug("Found {} OWA mailbox policies to evaluate", policies.size());

        List<CompletableFuture<PolicyChangeResult>> results = policies.stream()
                .filter(this::hasThirdPartyStorageEnabled)
                .map(this::updateOwaMailboxPolicy)
                .toList();

        logger.debug("Found {} policies that need remediation", results.size());

        if (results.isEmpty()) {
            logger.debug("No policies found with third-party storage enabled - requirement already met");
            return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId(), "All OWA mailbox policies already have third-party storage providers disabled"));
        }

        return combineResults(results);
    }

    @Override
    public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
        logger.info("Starting rollback for {} (Third-Party Storage Providers Prevention)", getPolicyId());
        try {
            List<ParameterChangeResult> changes = fixResult.getChanges();
            if (changes == null || changes.isEmpty()) {
                return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "No changes found in fix result"));
            }

            List<CompletableFuture<PolicyChangeResult>> results = new ArrayList<>();

            for (ParameterChangeResult change : changes) {
                if (change.getStatus() != ParameterChangeStatus.SUCCESS) {
                    logger.error("Rollback the policy: {} identity: {} skipped", getPolicyId(), change.getParameter());
                    results.add(CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(),
                            "Rollback the policy " + getPolicyId() + " identity: " + change.getParameter() + " skipped", List.of(change))));
                    continue;
                }

                String policyIdentity = change.getParameter();
                String previousValue = (String) change.getPrevValue();
                
                if (policyIdentity == null || policyIdentity.trim().isEmpty()) {
                    logger.error("Invalid parameter format - missing policy identity: {}", change.getParameter());
                    results.add(CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(),
                            "Invalid parameter format - missing policy identity: " + change.getParameter(), List.of(change))));
                    continue;
                }

                results.add(restoreThirdPartyStorageSetting(policyIdentity, previousValue, change));
            }

            return combineResults(results);
        } catch (Exception ex) {
            logger.error("Rollback the policy {} failed", getPolicyId(), ex);
            return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ex.getMessage()));
        }
    }

    private CompletableFuture<PolicyChangeResult> updateOwaMailboxPolicy(OwaMailboxPolicy policy) {
        logger.info("Disabling third-party storage providers for OWA policy: {}", policy.identity);

        // Determine the setting to update based on the specific third-party storage configuration
        Map<String, Object> setParameters = new HashMap<>();
        setParameters.put(ExoConstants.IDENTITY, policy.identity);
        
        // Configure third-party storage restrictions
        setParameters.put("AdditionalStorageProvidersAvailable", false);
        setParameters.put(ExoConstants.ERROR_ACTION, ExoConstants.ERROR_ACTION_STOP);

        PowerShellClient.CommandRequest setOwaPolicy = new PowerShellClient.CommandRequest(
                ExoConstants.SET_OWA_MAILBOX_POLICY, setParameters);

        return exchangeClient.executeCmdletCommand(setOwaPolicy)
                .thenApply(result -> {
                    String previousValue = policy.additionalStorageProvidersAvailable ? STORAGE_ENABLED_VALUE : STORAGE_DISABLED_VALUE;
                    String newValue = STORAGE_DISABLED_VALUE;

                    ParameterChangeResult paramChange = new ParameterChangeResult()
                            .timeStamp(Instant.now())
                            .parameter(policy.identity)
                            .prevValue(previousValue)
                            .newValue(newValue)
                            .status(ParameterChangeStatus.SUCCESS);

                    logger.info("Successfully disabled third-party storage providers for policy '{}'", policy.identity);
                    return IPolicyRemediator.success_(getPolicyId(), 
                            "Successfully disabled third-party storage providers for policy '" + policy.identity + "'", 
                            List.of(paramChange));
                })
                .exceptionally(ex -> {
                    logger.error("Failed to update OWA mailbox policy '{}': {}", policy.identity, ex.getMessage());
                    ParameterChangeResult paramChange = new ParameterChangeResult()
                            .timeStamp(Instant.now())
                            .parameter(policy.identity)
                            .prevValue(policy.additionalStorageProvidersAvailable ? STORAGE_ENABLED_VALUE : STORAGE_DISABLED_VALUE)
                            .newValue(policy.additionalStorageProvidersAvailable ? STORAGE_ENABLED_VALUE : STORAGE_DISABLED_VALUE) // No change due to failure
                            .status(ParameterChangeStatus.FAILED);
                    return IPolicyRemediator.failed_(getPolicyId(), 
                            "Failed to update OWA mailbox policy '" + policy.identity + "': " + ex.getMessage(), 
                            List.of(paramChange));
                });
    }

    private CompletableFuture<PolicyChangeResult> restoreThirdPartyStorageSetting(String policyIdentity, 
                                                                                 String previousValue, 
                                                                                 ParameterChangeResult originalChange) {
        logger.info("Restoring third-party storage setting for policy '{}' to previous value: {}", policyIdentity, previousValue);

        Map<String, Object> setParameters = new HashMap<>();
        setParameters.put(ExoConstants.IDENTITY, policyIdentity);
        setParameters.put("AdditionalStorageProvidersAvailable", Boolean.parseBoolean(previousValue));
        setParameters.put(ExoConstants.ERROR_ACTION, ExoConstants.ERROR_ACTION_STOP);

        PowerShellClient.CommandRequest setOwaPolicy = new PowerShellClient.CommandRequest(
                ExoConstants.SET_OWA_MAILBOX_POLICY, setParameters);

        return exchangeClient.executeCmdletCommand(setOwaPolicy)
                .thenApply(result -> {
                    ParameterChangeResult rollbackChange = new ParameterChangeResult()
                            .timeStamp(Instant.now())
                            .parameter(policyIdentity)
                            .prevValue((String) originalChange.getNewValue()) // Current state before rollback
                            .newValue(previousValue)   // Target state after rollback
                            .status(ParameterChangeStatus.SUCCESS);

                    logger.info("Successfully restored third-party storage setting for policy '{}'", policyIdentity);
                    return IPolicyRemediator.success_(getPolicyId(), 
                            "Successfully restored third-party storage setting for policy '" + policyIdentity + "'", 
                            List.of(rollbackChange));
                })
                .exceptionally(ex -> {
                    logger.error("Failed to restore third-party storage setting for policy '{}': {}", policyIdentity, ex.getMessage());
                    ParameterChangeResult rollbackChange = new ParameterChangeResult()
                            .timeStamp(Instant.now())
                            .parameter(policyIdentity)
                            .prevValue((String) originalChange.getNewValue())
                            .newValue((String) originalChange.getNewValue()) // No change due to failure
                            .status(ParameterChangeStatus.FAILED);
                    return IPolicyRemediator.failed_(getPolicyId(), 
                            "Failed to restore third-party storage setting for policy '" + policyIdentity + "': " + ex.getMessage(), 
                            List.of(rollbackChange));
                });
    }

    private CompletableFuture<PolicyChangeResult> combineResults(List<CompletableFuture<PolicyChangeResult>> futures) {
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> {
                    List<PolicyChangeResult> results = futures.stream()
                            .map(CompletableFuture::join)
                            .toList();

                    List<ParameterChangeResult> allChanges = results.stream()
                            .map(PolicyChangeResult::getChanges)
                            .filter(Objects::nonNull)
                            .flatMap(Collection::stream)
                            .filter(Objects::nonNull)
                            .toList();

                    // Count results by type
                    long successCount = results.stream()
                            .filter(r -> r.getResult() == RemediationResult.SUCCESS)
                            .count();
                    long failedCount = results.stream()
                            .filter(r -> r.getResult() == RemediationResult.FAILED)
                            .count();

                    // Determine overall result status
                    if (failedCount == 0) {
                        return IPolicyRemediator.success_(getPolicyId(), 
                            "Disabled third-party storage providers in " + successCount + " OWA mailbox policies to prevent data leakage through external storage", 
                            allChanges);
                    } else if (successCount == 0) {
                        return IPolicyRemediator.failed_(getPolicyId(), 
                            "Failed to disable third-party storage providers in all " + failedCount + " policies", 
                            allChanges);
                    } else {
                        return IPolicyRemediator.partial_success_(getPolicyId(),
                                "Fixed " + successCount + " policies, failed to fix " + failedCount + " policies",
                                allChanges);
                    }
                });
    }

    private List<OwaMailboxPolicy> getOwaMailboxPolicies() {
        logger.info("Retrieving OWA mailbox policies for third-party storage audit from configuration.");
        JsonNode configData = this.configNode.get(ExoConstants.CONFIG_KEY_OWA_THIRD_PARTY_STORAGE);

        if (configData == null || !configData.isArray()) {
            logger.error("OWA third-party storage configuration data not found or not an array in the configuration node.");
            return Collections.emptyList();
        }

        try {
            CollectionType collectionType = jsonMapper.getTypeFactory().constructCollectionType(List.class, OwaMailboxPolicy.class);
            List<OwaMailboxPolicy> result = jsonMapper.convertValue(configData, collectionType);
            logger.debug("Successfully converted {} OWA mailbox policies from configuration.", result.size());
            return result;
        } catch (Exception e) {
            logger.error("Failed to convert OWA mailbox policies from configuration node.", e);
            return Collections.emptyList();
        }
    }

    private boolean hasThirdPartyStorageEnabled(OwaMailboxPolicy policy) {
        if (policy.identity == null) {
            logger.debug("Policy has no identity");
            return false;
        }
        
        boolean hasThirdPartyEnabled = policy.additionalStorageProvidersAvailable;
        logger.info("Policy '{}' has third-party storage enabled: {}", policy.identity, hasThirdPartyEnabled);
        
        return hasThirdPartyEnabled;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    private static class OwaMailboxPolicy {
        @JsonProperty("Identity")
        public String identity;
        
        @JsonProperty("AdditionalStorageProvidersAvailable")  
        public boolean additionalStorageProvidersAvailable;

        @JsonProperty("AllowedFileTypes")
        public List<String> allowedFileTypes;

        @JsonProperty("AllowedMimeTypes")
        public List<String> allowedMimeTypes;

        /**
         * Default constructor required for Jackson JSON deserialization.
         * This constructor initializes all collections as empty lists to prevent null pointer exceptions.
         */
        @SuppressWarnings("unused")
        public OwaMailboxPolicy() {
            this.allowedFileTypes = new ArrayList<>();
            this.allowedMimeTypes = new ArrayList<>();
        }

        public OwaMailboxPolicy(String identity, boolean additionalStorageProvidersAvailable, 
                               List<String> allowedFileTypes, List<String> allowedMimeTypes) {
            this.identity = identity;
            this.additionalStorageProvidersAvailable = additionalStorageProvidersAvailable;
            this.allowedFileTypes = allowedFileTypes != null ? new ArrayList<>(allowedFileTypes) : new ArrayList<>();
            this.allowedMimeTypes = allowedMimeTypes != null ? new ArrayList<>(allowedMimeTypes) : new ArrayList<>();
        }
    }
}