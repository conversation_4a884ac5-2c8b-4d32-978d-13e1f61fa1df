package io.syrix.products.microsoft.teams.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.IPolicyRemediatorRollback;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeStatus;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediationResult;
import io.syrix.datamodel.task.remediation.teams.TeamsRemediationConfig;
import io.syrix.products.microsoft.teams.model.TeamsMeetingPolicy;
import io.syrix.protocols.client.PowerShellTeamsClient;
import io.syrix.protocols.client.teams.powershell.command.CsTeamsCommand;
import io.syrix.protocols.client.teams.powershell.command.types.MeetingPolicy;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

@PolicyRemediator("MS.TEAMS.1.8v1")
public class TeamsAnonymousChatRemediator extends TeamsRemediatorBase implements IPolicyRemediatorRollback {

	public TeamsAnonymousChatRemediator(PowerShellTeamsClient client, ObjectNode configNode, TeamsRemediationConfig remediationConfig) {
		super(client, configNode, remediationConfig);
	}

	public TeamsAnonymousChatRemediator(PowerShellTeamsClient client) {
		super(client, null, null);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return remediate_().thenApply(jsonMapper::valueToTree);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		List<TeamsMeetingPolicy> policies = getTeamsMeetingPolicies();

		if (policies.isEmpty()) {
			logger.error("No meeting policies found in configuration");
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "No meeting policies found in configuration"));
		}

		List<CompletableFuture<PolicyChangeResult>> results = policies.stream()
				.filter(policy -> !"EnabledExceptAnonymous".equals(policy.meetingChatEnabledType))
				.filter(policy -> !policy.identity.startsWith("Tag:")) // policy with the "Tag:" only read
				.map(this::fixPolicy_)
				.toList();

		if (results.isEmpty()) {
			return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId()));
		}

		return combineResults(results);
	}

	private CompletableFuture<PolicyChangeResult> fixPolicy_(TeamsMeetingPolicy policy) {
		MeetingPolicy meetingPolicy = new MeetingPolicy();
		meetingPolicy.identity = policy.identity;
		meetingPolicy.meetingChatEnabledType = "EnabledExceptAnonymous";

		ParameterChangeResult chatEnabledParam = new ParameterChangeResult()
				.timeStamp(Instant.now())
				.parameter("meetingChatEnabledType: " + policy.identity)
				.prevValue(policy.meetingChatEnabledType)
				.newValue(meetingPolicy.meetingChatEnabledType);

		return client.execute(CsTeamsCommand.CsTeamsMeetingPolicy.SET(meetingPolicy))
				.thenApply(meetingPolicies -> {
					chatEnabledParam.status(ParameterChangeStatus.SUCCESS);
					return IPolicyRemediator.success_(getPolicyId(), "Meeting Policy fixed: " + policy.identity, List.of(chatEnabledParam));
				})
				.exceptionally(ex -> {
					logger.warn("Failed to update Meeting Policy: {} , errMsg: {}", policy.identity, ex.getMessage());
					chatEnabledParam.status(ParameterChangeStatus.FAILED);
					return IPolicyRemediator.failed_(getPolicyId(), "Failed to update Meeting Policy: " + ex.getMessage(), List.of(chatEnabledParam));
				});
	}

	private CompletableFuture<PolicyChangeResult> combineResults(List<CompletableFuture<PolicyChangeResult>> futures) {
		return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
				.thenApply(v -> {
					List<PolicyChangeResult> results = futures.stream()
							.map(CompletableFuture::join)
							.toList();

					List<ParameterChangeResult> allChanges = results.stream()
							.map(PolicyChangeResult::getChanges)
							.filter(Objects::nonNull)
							.flatMap(Collection::stream)
							.filter(Objects::nonNull)
							.toList();

					// Count results by type
					long successCount = results.stream()
							.filter(r -> r.getResult() == RemediationResult.SUCCESS)
							.count();
					long failedCount = results.stream()
							.filter(r -> r.getResult() == RemediationResult.FAILED)
							.count();

					// Determine overall result status
					if (failedCount == 0) {
						return IPolicyRemediator.success_(getPolicyId(), "All meeting policies fixed successfully", allChanges);
					} else if (successCount == 0) {
						return IPolicyRemediator.failed_(getPolicyId(), "Failed to fix any meeting policies", allChanges);
					} else {
						return IPolicyRemediator.partial_success_(getPolicyId(),
								"Fixed " + successCount + " policies, failed to fix " + failedCount + " policies",
								allChanges);
					}
				});
	}

	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		try {
			List<ParameterChangeResult> changes = fixResult.getChanges();
			List<CompletableFuture<PolicyChangeResult>> results = new ArrayList<>();

			for (ParameterChangeResult change : changes) {
				String identity = change.getParameter().split(": ")[1];
				String meetingChatEnabledType = change.getPrevValue().toString();
				String prevMeetingChatEnabledType = change.getNewValue().toString();

				MeetingPolicy meetingPolicy = new MeetingPolicy();
				meetingPolicy.identity = identity;
				meetingPolicy.meetingChatEnabledType = meetingChatEnabledType;

				ParameterChangeResult chatEnabledParam = new ParameterChangeResult()
						.timeStamp(Instant.now())
						.parameter("meetingChatEnabledType: " + identity)
						.prevValue(prevMeetingChatEnabledType)
						.newValue(meetingChatEnabledType);

				if (change.getStatus() != ParameterChangeStatus.SUCCESS) {
					logger.error("Rollback the policy: {} identity: {} skipped", getPolicyId(), identity);
					chatEnabledParam.status(ParameterChangeStatus.FAILED);
					results.add(CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(),
							"Rollback the policy " + getPolicyId() + " identity: " + identity + " skipped", List.of(chatEnabledParam))));
					continue;
				}

				CompletableFuture<PolicyChangeResult> result = client.execute(CsTeamsCommand.CsTeamsMeetingPolicy.SET(meetingPolicy))
						.thenApply(jsonNode -> {
							chatEnabledParam.status(ParameterChangeStatus.SUCCESS);
							return IPolicyRemediator.success_(getPolicyId(), "Successfully rolled back anonymous chat policy", List.of(chatEnabledParam));
						})
						.exceptionally(ex -> {
							chatEnabledParam.status(ParameterChangeStatus.FAILED);
							logger.error("Exception during anonymous chat policy rollback", ex);
							return IPolicyRemediator.failed_(getPolicyId(), ex.getMessage(), List.of(chatEnabledParam));
						});

				results.add(result);
			}

			return combineResults(results);
		} catch (Exception ex) {
			logger.error("Rollback the policy {} failed", getPolicyId(), ex);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ex.getMessage()));
		}
	}
}