package io.syrix.products.microsoft.exo.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.datamodel.task.remediation.exchange.ExchangeRemediationConfig;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.IPolicyRemediatorRollback;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeStatus;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.exo.ExoConstants;
import io.syrix.products.microsoft.exo.remediation.context.ExchangeRemediationContext;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;

import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Remediator for MS.EXO.7.2v1 that ensures MailTips are enabled organization-wide.
 * <p>
 * This class implements the following security controls:
 * - Enables MailTips to provide user warnings about external recipients
 * - Enables mailbox-sourced tips for automatic replies and full mailbox warnings
 * - Verifies the current MailTips configuration before applying changes
 * <p>
 * This implementation is part of Section 7 "External Sender Warnings" alongside MS.EXO.7.1v1.
 */
@PolicyRemediator("MS.EXO.7.2v1")
public class ExchangeMailTipsRemediator extends ExchangeBaseRemediator implements IPolicyRemediatorRollback {

	private static final String MAILTIPS_CONFIG_NAME = "MailTips Configuration";

	private static final Map<String, Object> MAILTIPS_ENABLE_PARAMS = Map.of(
			ExoConstants.MAILTIPS_ALL_TIPS_ENABLED, true,
			ExoConstants.MAILTIPS_MAILBOX_SOURCED_TIPS_ENABLED, true,
			ExoConstants.MAILTIPS_EXTERNAL_RECIPIENTS_ENABLED, true,
			ExoConstants.MAILTIPS_GROUP_METRICS_ENABLED, true,
			ExoConstants.ERROR_ACTION, ExoConstants.ERROR_ACTION_STOP
	);

	public ExchangeMailTipsRemediator(MicrosoftGraphClient graphClient,
									  PowerShellClient exchangeClient,
									  ObjectNode configNode,
									  ExchangeRemediationContext exchangeRemediationContext,
									  ExchangeRemediationConfig remediationConfig) {
		super(graphClient, exchangeClient, configNode, exchangeRemediationContext, remediationConfig);
	}

	// Constructor for Rollback interface
	public ExchangeMailTipsRemediator(MicrosoftGraphClient graphClient, PowerShellClient exchangeClient) {
		this(graphClient, exchangeClient, null, null, null);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return updateMailTipsProperties().thenApply(jsonMapper::valueToTree);
	}

	public CompletableFuture<PolicyChangeResult> updateMailTipsProperties() {
		logger.info("Starting remediation for {} (MailTips)", getPolicyId());

		OrganizationConfig currentConfig = loadOrganizationConfig();
		if (currentConfig == null) {
			return CompletableFuture.completedFuture(
					IPolicyRemediator.failed_(getPolicyId(), ExoConstants.ERROR_NO_MAILTIPS_CONFIG));
		}

		if (currentConfig.mailTipsAllTipsEnabled != null && currentConfig.mailTipsAllTipsEnabled &&
				currentConfig.mailTipsMailboxSourcedTipsEnabled != null && currentConfig.mailTipsMailboxSourcedTipsEnabled &&
				currentConfig.mailTipsExternalRecipientsTipsEnabled != null && currentConfig.mailTipsExternalRecipientsTipsEnabled) {
			logger.info("MailTips are already enabled for the organization");
			return CompletableFuture.completedFuture(
					IPolicyRemediator.requirementMet_(getPolicyId(), ExoConstants.SUCCESS_MAILTIPS_ALREADY_ENABLED));
		}

		// Track parameter changes for rollback
		List<ParameterChangeResult> changes = new ArrayList<>();

		// Track MailTipsAllTipsEnabled change
		boolean currentAllTips = currentConfig.mailTipsAllTipsEnabled != null && currentConfig.mailTipsAllTipsEnabled;
		if (!currentAllTips) {
			changes.add(new ParameterChangeResult()
					.timeStamp(Instant.now())
					.parameter(ExoConstants.MAILTIPS_ALL_TIPS_ENABLED)
					.prevValue(currentConfig.mailTipsAllTipsEnabled)
					.newValue(true)
					.status(ParameterChangeStatus.UNKNOWN));
		}

		// Track MailTipsMailboxSourcedTipsEnabled change
		boolean currentSourced = currentConfig.mailTipsMailboxSourcedTipsEnabled != null && currentConfig.mailTipsMailboxSourcedTipsEnabled;
		if (!currentSourced) {
			changes.add(new ParameterChangeResult()
					.timeStamp(Instant.now())
					.parameter(ExoConstants.MAILTIPS_MAILBOX_SOURCED_TIPS_ENABLED)
					.prevValue(currentConfig.mailTipsMailboxSourcedTipsEnabled)
					.newValue(true)
					.status(ParameterChangeStatus.UNKNOWN));
		}

		// Track MailTipsExternalRecipientsTipsEnabled change
		boolean currentExternal = currentConfig.mailTipsExternalRecipientsTipsEnabled != null && currentConfig.mailTipsExternalRecipientsTipsEnabled;
		if (!currentExternal) {
			changes.add(new ParameterChangeResult()
					.timeStamp(Instant.now())
					.parameter(ExoConstants.MAILTIPS_EXTERNAL_RECIPIENTS_ENABLED)
					.prevValue(currentConfig.mailTipsExternalRecipientsTipsEnabled)
					.newValue(true)
					.status(ParameterChangeStatus.UNKNOWN));
		}

		logger.info("Enabling MailTips for organization: MailTipsAllTipsEnabled={}, MailTipsMailboxSourcedTipsEnabled={}, MailTipsExternalRecipientsTipsEnabled={}",
				!currentAllTips, !currentSourced, !currentExternal);

		PowerShellClient.CommandRequest setOrgConfigRequest =
				new PowerShellClient.CommandRequest(ExoConstants.SET_ORGANIZATION_CONFIG, MAILTIPS_ENABLE_PARAMS);

		return exchangeClient.executeCmdletCommand(setOrgConfigRequest)
				.thenApply(result -> {
					logger.info("Successfully enabled MailTips for the organization");
					// Mark all changes as successful
					changes.forEach(change -> change.status(ParameterChangeStatus.SUCCESS));
					return IPolicyRemediator.success_(getPolicyId(), ExoConstants.SUCCESS_MAILTIPS_ENABLED, changes);
				})
				.exceptionally(throwable -> {
					logger.error("Failed to enable MailTips", throwable);
					// Mark all changes as failed
					changes.forEach(change -> change.status(ParameterChangeStatus.FAILED));
					return IPolicyRemediator.failed_(getPolicyId(), "Failed to enable MailTips: " + throwable.getMessage(), changes);
				});
	}

	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		logger.info("Starting rollback for {} (MailTips)", getPolicyId());

		try {
			List<ParameterChangeResult> changes = fixResult.getChanges();
			if (changes == null || changes.isEmpty()) {
				return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "No changes found in fix result"));
			}

			// Build rollback parameters from original values
			Map<String, Object> rollbackParams = new HashMap<>();
			rollbackParams.put(ExoConstants.ERROR_ACTION, ExoConstants.ERROR_ACTION_STOP);

			for (ParameterChangeResult change : changes) {
				if (change.getStatus() == ParameterChangeStatus.SUCCESS) {
					rollbackParams.put(change.getParameter(), change.getPrevValue());
					logger.info("Rolling back parameter {} from {} to {}",
							change.getParameter(), change.getNewValue(), change.getPrevValue());
				}
			}

			if (rollbackParams.size() == 1) { // Only ERROR_ACTION present
				logger.info("No successful changes to rollback");
				return CompletableFuture.completedFuture(IPolicyRemediator.success_(getPolicyId(), "No changes to rollback"));
			}

			PowerShellClient.CommandRequest rollbackRequest =
					new PowerShellClient.CommandRequest(ExoConstants.SET_ORGANIZATION_CONFIG, rollbackParams);

			return exchangeClient.executeCmdletCommand(rollbackRequest)
					.thenApply(result -> {
						logger.info("MailTips rollback completed successfully");
						return IPolicyRemediator.success_(getPolicyId(), "MailTips settings restored to original values");
					})
					.exceptionally(throwable -> {
						logger.error("Error during MailTips rollback", throwable);
						return IPolicyRemediator.failed_(getPolicyId(), "Error during MailTips rollback: " + throwable.getMessage());
					});
		} catch (Exception e) {
			logger.error("Error processing rollback parameters", e);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "Error processing rollback: " + e.getMessage()));
		}
	}
}