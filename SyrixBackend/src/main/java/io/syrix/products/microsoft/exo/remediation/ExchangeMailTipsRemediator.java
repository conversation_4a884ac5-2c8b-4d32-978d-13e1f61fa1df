package io.syrix.products.microsoft.exo.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.datamodel.task.remediation.exchange.ExchangeRemediationConfig;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.IPolicyRemediatorRollback;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeStatus;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.exo.ExoConstants;
import io.syrix.products.microsoft.exo.remediation.context.ExchangeRemediationContext;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;

import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Remediator for MS.EXO.7.2v1 that ensures MailTips are enabled organization-wide.
 * <p>
 * This class implements the following security controls:
 * - Enables MailTips to provide user warnings about external recipients
 * - Enables mailbox-sourced tips for automatic replies and full mailbox warnings
 * - Verifies the current MailTips configuration before applying changes
 * - Validates that the configuration was successfully applied
 * <p>
 * This implementation is part of Section 7 "External Sender Warnings" alongside MS.EXO.7.1v1.
 */
@PolicyRemediator("MS.EXO.7.2v1")
public class ExchangeMailTipsRemediator extends ExchangeBaseRemediator implements IPolicyRemediatorRollback {

	private static final String MAILTIPS_CONFIG_NAME = "MailTips Configuration";

	private static final Map<String, Object> MAILTIPS_ENABLE_PARAMS = Map.of(
			ExoConstants.MAILTIPS_ALL_TIPS_ENABLED, true,
			ExoConstants.MAILTIPS_MAILBOX_SOURCED_TIPS_ENABLED, true,
			ExoConstants.MAILTIPS_EXTERNAL_RECIPIENTS_ENABLED, true,
			ExoConstants.ERROR_ACTION, ExoConstants.ERROR_ACTION_STOP
	);

	public ExchangeMailTipsRemediator(MicrosoftGraphClient graphClient,
									  PowerShellClient exchangeClient,
									  ObjectNode configNode,
									  ExchangeRemediationContext exchangeRemediationContext,
									  ExchangeRemediationConfig remediationConfig) {
		super(graphClient, exchangeClient, configNode, exchangeRemediationContext, remediationConfig);
	}

	// Constructor for Rollback interface
	public ExchangeMailTipsRemediator(MicrosoftGraphClient graphClient, PowerShellClient exchangeClient) {
		this(graphClient, exchangeClient, null, null, null);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return updateMailTipsProperties().thenApply(jsonMapper::valueToTree);
	}

	public CompletableFuture<PolicyChangeResult> updateMailTipsProperties() {
		logger.info("Starting remediation for {} (MailTips)", getPolicyId());

		OrganizationConfig currentConfig = loadOrganizationConfig();
		if (currentConfig == null) {
			return CompletableFuture.completedFuture(
					IPolicyRemediator.failed_(getPolicyId(), ExoConstants.ERROR_NO_MAILTIPS_CONFIG));
		}

		if (currentConfig.mailTipsAllTipsEnabled != null && currentConfig.mailTipsAllTipsEnabled &&
				currentConfig.mailTipsMailboxSourcedTipsEnabled != null && currentConfig.mailTipsMailboxSourcedTipsEnabled &&
				currentConfig.mailTipsExternalRecipientsTipsEnabled != null && currentConfig.mailTipsExternalRecipientsTipsEnabled) {
			logger.info("MailTips are already enabled for the organization");
			return CompletableFuture.completedFuture(
					IPolicyRemediator.requirementMet_(getPolicyId(), ExoConstants.SUCCESS_MAILTIPS_ALREADY_ENABLED));
		}

		// Track parameter changes for rollback
		List<ParameterChangeResult> changes = new ArrayList<>();

		// Track MailTipsAllTipsEnabled change
		boolean currentAllTips = currentConfig.mailTipsAllTipsEnabled != null && currentConfig.mailTipsAllTipsEnabled;
		if (!currentAllTips) {
			changes.add(new ParameterChangeResult()
					.timeStamp(Instant.now())
					.parameter(ExoConstants.MAILTIPS_ALL_TIPS_ENABLED)
					.prevValue(currentConfig.mailTipsAllTipsEnabled)
					.newValue(true)
					.status(ParameterChangeStatus.UNKNOWN));
		}

		// Track MailTipsMailboxSourcedTipsEnabled change
		boolean currentSourced = currentConfig.mailTipsMailboxSourcedTipsEnabled != null && currentConfig.mailTipsMailboxSourcedTipsEnabled;
		if (!currentSourced) {
			changes.add(new ParameterChangeResult()
					.timeStamp(Instant.now())
					.parameter(ExoConstants.MAILTIPS_MAILBOX_SOURCED_TIPS_ENABLED)
					.prevValue(currentConfig.mailTipsMailboxSourcedTipsEnabled)
					.newValue(true)
					.status(ParameterChangeStatus.UNKNOWN));
		}

		// Track MailTipsExternalRecipientsTipsEnabled change
		boolean currentExternal = currentConfig.mailTipsExternalRecipientsTipsEnabled != null && currentConfig.mailTipsExternalRecipientsTipsEnabled;
		if (!currentExternal) {
			changes.add(new ParameterChangeResult()
					.timeStamp(Instant.now())
					.parameter(ExoConstants.MAILTIPS_EXTERNAL_RECIPIENTS_ENABLED)
					.prevValue(currentConfig.mailTipsExternalRecipientsTipsEnabled)
					.newValue(true)
					.status(ParameterChangeStatus.UNKNOWN));
		}

		logger.info("Enabling MailTips for organization: MailTipsAllTipsEnabled={}, MailTipsMailboxSourcedTipsEnabled={}, MailTipsExternalRecipientsTipsEnabled={}",
				!currentAllTips, !currentSourced, !currentExternal);

		PowerShellClient.CommandRequest setOrgConfigRequest =
				new PowerShellClient.CommandRequest(ExoConstants.SET_ORGANIZATION_CONFIG, MAILTIPS_ENABLE_PARAMS);

		return exchangeClient.executeCmdletCommand(setOrgConfigRequest)
				.thenCompose(result -> verifyMailTipsEnabled())
				.thenApply(verificationResult -> {
					if (verificationResult.success) {
						logger.info("MailTips enabled successfully for the organization");
						// Mark all changes as successful
						changes.forEach(change -> change.status(ParameterChangeStatus.SUCCESS));
						return IPolicyRemediator.success_(getPolicyId(), ExoConstants.SUCCESS_MAILTIPS_ENABLED, changes);
					} else {
						logger.error("Failed to enable MailTips: {}", verificationResult.message);
						// Mark all changes as failed
						changes.forEach(change -> change.status(ParameterChangeStatus.FAILED));
						return IPolicyRemediator.failed_(getPolicyId(), verificationResult.message, changes);
					}
				})
				.exceptionally(throwable -> {
					logger.error("Error enabling MailTips", throwable);
					return IPolicyRemediator.failed_(getPolicyId(), "Error enabling MailTips: " + throwable.getMessage());
				});
	}

	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		logger.info("Starting rollback for {} (MailTips)", getPolicyId());

		try {
			List<ParameterChangeResult> changes = fixResult.getChanges();
			if (changes == null || changes.isEmpty()) {
				return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "No changes found in fix result"));
			}

			// Build rollback parameters from original values
			Map<String, Object> rollbackParams = new HashMap<>();
			rollbackParams.put(ExoConstants.ERROR_ACTION, ExoConstants.ERROR_ACTION_STOP);

			for (ParameterChangeResult change : changes) {
				if (change.getStatus() == ParameterChangeStatus.SUCCESS) {
					rollbackParams.put(change.getParameter(), change.getPrevValue());
					logger.info("Rolling back parameter {} from {} to {}",
							change.getParameter(), change.getNewValue(), change.getPrevValue());
				}
			}

			if (rollbackParams.size() == 1) { // Only ERROR_ACTION present
				logger.info("No successful changes to rollback");
				return CompletableFuture.completedFuture(IPolicyRemediator.success_(getPolicyId(), "No changes to rollback"));
			}

			PowerShellClient.CommandRequest rollbackRequest =
					new PowerShellClient.CommandRequest(ExoConstants.SET_ORGANIZATION_CONFIG, rollbackParams);

			return exchangeClient.executeCmdletCommand(rollbackRequest)
					.thenApply(result -> {
						logger.info("MailTips rollback completed successfully");
						return IPolicyRemediator.success_(getPolicyId(), "MailTips settings restored to original values");
					})
					.exceptionally(throwable -> {
						logger.error("Error during MailTips rollback", throwable);
						return IPolicyRemediator.failed_(getPolicyId(), "Error during MailTips rollback: " + throwable.getMessage());
					});
		} catch (Exception e) {
			logger.error("Error processing rollback parameters", e);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "Error processing rollback: " + e.getMessage()));
		}
	}


	/**
	 * Verifies that MailTips have been successfully enabled by checking the organization configuration.
	 *
	 * @return CompletableFuture containing verification result
	 */
	private CompletableFuture<VerificationResult> verifyMailTipsEnabled() {
		logger.info("Verifying MailTips configuration after remediation");

		// Add a small delay to allow Exchange to propagate changes
		return CompletableFuture
				.runAsync(() -> {
					try {
						Thread.sleep(10000); // 10 second delay
						logger.info("Waited for 15 seconds for Exchange Online propagation...");
					} catch (InterruptedException e) {
						Thread.currentThread().interrupt();
					}
				})
				.thenCompose(v -> {
					PowerShellClient.CommandRequest getOrgConfigRequest =
							new PowerShellClient.CommandRequest(ExoConstants.GET_ORGANIZATION_CONFIG, Map.of());
					return exchangeClient.executeCmdletCommand(getOrgConfigRequest);
				})
				.thenApply(result -> {
					if (result.isArray() && !result.isEmpty()) {
						JsonNode orgConfig = result.get(0);

						// Debug: Log the entire orgConfig structure
						logger.debug("Full organization config JSON: {}", orgConfig.toString());

						// More robust boolean checking - handle null, missing, and different value types
						boolean mailTipsAllEnabled = getBooleanValue(orgConfig, ExoConstants.MAILTIPS_ALL_TIPS_ENABLED);
						boolean mailTipsSourcedEnabled = getBooleanValue(orgConfig, ExoConstants.MAILTIPS_MAILBOX_SOURCED_TIPS_ENABLED);
						boolean mailTipsExternalEnabled = getBooleanValue(orgConfig, ExoConstants.MAILTIPS_EXTERNAL_RECIPIENTS_ENABLED);
						boolean MailTipsGroupMetricsEnabled = getBooleanValue(orgConfig, ExoConstants.MAILTIPS_GROUP_METRICS_ENABLED);
						// Debug logging to see actual values during verification
						logger.info("Verification reading: AllTipsEnabled={}, MailboxSourcedTipsEnabled={}, ExternalRecipientsTipsEnabled={}",
								mailTipsAllEnabled, mailTipsSourcedEnabled, mailTipsExternalEnabled);
						logger.debug("Raw JSON values: AllTips={}, MailboxSourced={}, External={}, Group Metrics {}",
								orgConfig.get(ExoConstants.MAILTIPS_ALL_TIPS_ENABLED),
								orgConfig.get(ExoConstants.MAILTIPS_MAILBOX_SOURCED_TIPS_ENABLED),
								orgConfig.get(ExoConstants.MAILTIPS_EXTERNAL_RECIPIENTS_ENABLED),
								orgConfig.get(ExoConstants.MAILTIPS_GROUP_METRICS_ENABLED));

						// Log field existence
						logger.debug("Field existence check: AllTips={}, MailboxSourced={}, External={}, Group Metrics {}",
								orgConfig.has(ExoConstants.MAILTIPS_ALL_TIPS_ENABLED),
								orgConfig.has(ExoConstants.MAILTIPS_MAILBOX_SOURCED_TIPS_ENABLED),
								orgConfig.has(ExoConstants.MAILTIPS_EXTERNAL_RECIPIENTS_ENABLED),
								orgConfig.get(ExoConstants.MAILTIPS_GROUP_METRICS_ENABLED));

						if (mailTipsAllEnabled && mailTipsSourcedEnabled && mailTipsExternalEnabled) {
							return new VerificationResult(true, "MailTips verified as enabled");
						} else {
							return new VerificationResult(false,
									String.format("MailTips verification failed: AllTipsEnabled=%b, MailboxSourcedTipsEnabled=%b, ExternalRecipientsTipsEnabled=%b",
											mailTipsAllEnabled, mailTipsSourcedEnabled, mailTipsExternalEnabled));
						}
					} else {
						logger.error("Invalid organization config response: isArray={}, isEmpty={}",
								result.isArray(), result.isEmpty());
						return new VerificationResult(false, "Unable to retrieve organization configuration for verification");
					}
				})
				.exceptionally(throwable -> {
					logger.error("Error during MailTips verification", throwable);
					return new VerificationResult(false, "Error during verification: " + throwable.getMessage());
				});
	}

	/**
	 * Safely extracts boolean values from JsonNode, handling various data types and null values.
	 * PowerShell might return different types (boolean, string, number) depending on the context.
	 */
	private boolean getBooleanValue(JsonNode node, String fieldName) {
		if (!node.has(fieldName)) {
			logger.debug("Field '{}' not found in JSON", fieldName);
			return false;
		}

		JsonNode fieldNode = node.get(fieldName);
		if (fieldNode == null || fieldNode.isNull()) {
			logger.debug("Field '{}' is null", fieldName);
			return false;
		}

		// Handle different data types that PowerShell might return
		if (fieldNode.isBoolean()) {
			return fieldNode.asBoolean();
		} else if (fieldNode.isTextual()) {
			String textValue = fieldNode.asText().toLowerCase().trim();
			boolean result = "true".equals(textValue) || "1".equals(textValue) || "yes".equals(textValue);
			logger.debug("Field '{}' is text '{}', interpreted as {}", fieldName, fieldNode.asText(), result);
			return result;
		} else if (fieldNode.isNumber()) {
			boolean result = fieldNode.asInt() != 0;
			logger.debug("Field '{}' is number {}, interpreted as {}", fieldName, fieldNode.asInt(), result);
			return result;
		} else {
			logger.warn("Field '{}' has unexpected type: {}, value: {}", fieldName, fieldNode.getNodeType(), fieldNode.toString());
			return false;
		}
	}

	/**
	 * Inner class for verification results
	 */
	private static class VerificationResult {
		public final boolean success;
		public final String message;

		public VerificationResult(boolean success, String message) {
			this.success = success;
			this.message = message;
		}
	}
}